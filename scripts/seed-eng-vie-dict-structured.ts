
import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import csv from 'csv-parser';

const prisma = new PrismaClient();

/**
 * Parses the complex 'vietnamese_analytics' column to extract structured data.
 * @param analytics The raw string from the CSV's second column.
 * @returns An object containing the extracted ipa, pos, and vietnamese definitions.
 */
function parseVietnameseAnalytics(analytics: string): { ipa: string | null; pos: string | null; vietnamese: string[] } {
    let remaining = analytics.trim();
    let ipa: string | null = null;
    let pos: string | null = null;

    // 1. Extract IPA pronunciation, which is usually enclosed in square brackets.
    // Example: [ei, ə] or [ɑ:lɑ:kɑ:t]
    const ipaMatch = remaining.match(/^\[(.*?)\]/);
    if (ipaMatch) {
        ipa = ipaMatch[1].trim();
        remaining = remaining.substring(ipaMatch[0].length).trim();
        // Clean up leading comma if present after IPA
        if (remaining.startsWith(',')) {
            remaining = remaining.substring(1).trim();
        }
    }

    // 2. Define a list of known Part-of-Speech tags and special markers.
    const posTags = [
        'danh từ', 'giới từ', 'phó từ', 'tính từ', 'ngoại động từ',
        'nội động từ', 'interj', '(tech)', '(thông tục)', 'hình thái',
        'hậu tố', '(vt của', '(viết tắt)', 'xem', 'động từ'
    ];

    // 3. Attempt to identify the Part of Speech from the beginning of the remaining string.
    const foundTag = posTags.find(tag => remaining.toLowerCase().startsWith(tag));
    if (foundTag) {
        pos = foundTag;
        remaining = remaining.substring(foundTag.length).trim();
    }

    // 4. The rest of the string contains the Vietnamese definitions.
    // They are often separated by '|' or '|-'. We split and clean them.
    const definitions = remaining
        .split(/\|-?/)
        .map(def => def.replace(/[|+@]/g, '').trim()) // Clean up extra symbols
        .filter(Boolean); // Remove any empty strings resulting from splits

    return { ipa, pos, vietnamese: definitions };
}

async function main() {
    console.log('Starting structured seeding of EngVieDict...');

    const stream = fs.createReadStream('scripts/dict-data/English - Vietnamese.csv', { encoding: 'utf16le' })
        .pipe(csv({ headers: ['english', 'vietnamese_analytics'], skipLines: 1, separator: ',' }));

    let rowCount = 0;
    let processedCount = 0;

    for await (const row of stream) {
        rowCount++;
        try {
            const english = row.english?.trim();
            const analytics = row.vietnamese_analytics?.trim();

            if (!english || !analytics) {
                // console.warn(`Skipping invalid or empty row: ${JSON.stringify(row)}`);
                continue;
            }

            const { ipa, pos, vietnamese } = parseVietnameseAnalytics(analytics);

            if (vietnamese.length === 0) {
                // console.warn(`No definitions found for "${english}". Raw: "${analytics}"`);
                continue;
            }

            await prisma.engVieDict.upsert({
                where: { english: english },
                update: { pos, ipa, vietnamese },
                create: { english, pos, ipa, vietnamese },
            });

            processedCount++;
            if (processedCount % 1000 === 0) {
                console.log(`Processed ${processedCount} / ${rowCount} rows...`);
            }

        } catch (error) {
            console.error(`Error processing row #${rowCount}: ${JSON.stringify(row)}`, error);
        }
    }

    console.log('Finished structured seeding of EngVieDict.');
    console.log(`Total rows read: ${rowCount}`);
    console.log(`Total rows successfully processed and upserted: ${processedCount}`);
}

main()
    .catch((e) => {
        console.error('An unexpected error occurred:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });
